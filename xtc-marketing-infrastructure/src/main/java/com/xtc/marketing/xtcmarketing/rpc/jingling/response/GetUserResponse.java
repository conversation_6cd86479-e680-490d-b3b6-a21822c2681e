package com.xtc.marketing.xtcmarketing.rpc.jingling.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 获取用户信息响应DTO
 */
@Getter
@Setter
@ToString
public class GetUserResponse extends BaseJinglingResponse {

    /**
     * 响应数据
     */
    @SerializedName("data")
    private ResponseData data;

    /**
     * 响应数据
     */
    @Getter
    @Setter
    @ToString
    public static class ResponseData {
        
        /**
         * 总记录数
         */
        @SerializedName("totalCount")
        private Integer totalCount;
        
        /**
         * 用户列表
         */
        @SerializedName("list")
        private List<UserInfo> list;
    }

    /**
     * 用户信息
     */
    @Getter
    @Setter
    @ToString
    public static class UserInfo {
        
        /**
         * 用户ID
         */
        @SerializedName("id")
        private String id;
        
        /**
         * 微信用户ID
         */
        @SerializedName("recvPerfWechatUserid")
        private String recvPerfWechatUserid;
        
        /**
         * 用户名
         */
        @SerializedName("userName")
        private String userName;
        
        /**
         * 手机号
         */
        @SerializedName("phone")
        private String phone;
        
        /**
         * 邮箱
         */
        @SerializedName("email")
        private String email;
        
        /**
         * 状态
         */
        @SerializedName("status")
        private String status;
        
        /**
         * 创建时间
         */
        @SerializedName("createTime")
        private String createTime;
        
        /**
         * 更新时间
         */
        @SerializedName("updateTime")
        private String updateTime;
    }

}
