package com.xtc.marketing.xtcmarketing.rpc.jingling.request;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.xtcmarketing.rpc.jingling.response.GetUserResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpMethod;

import java.util.List;

/**
 * 获取用户信息请求DTO
 */
@Getter
@Setter
@ToString
public class GetUserRequest extends BaseJinglingRequest<GetUserResponse> {

    /**
     * 页面大小
     */
    @SerializedName("pageSize")
    private Integer pageSize;

    /**
     * 页面索引
     */
    @SerializedName("pageIndex")
    private Integer pageIndex;

    /**
     * 查询条件
     */
    @SerializedName("andQuery")
    private List<QueryCondition> andQuery;

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.POST;
    }

    @Override
    public String getRequestUrl() {
        return "https://jl-gateway.okii.com/api/sync/customer/v1/psysAgentList";
    }

    @Override
    public Class<GetUserResponse> getResponseClass() {
        return GetUserResponse.class;
    }

    /**
     * 查询条件
     */
    @Getter
    @Setter
    @ToString
    public static class QueryCondition {
        
        /**
         * 字段名
         */
        @SerializedName("field")
        private String field;
        
        /**
         * 比较类型
         */
        @SerializedName("compareType")
        private String compareType;
        
        /**
         * 值
         */
        @SerializedName("value")
        private String value;
    }

}
