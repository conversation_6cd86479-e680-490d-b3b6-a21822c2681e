package com.xtc.marketing.xtcmarketing.rpc.jingling.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 精灵基础响应DTO
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public abstract class BaseJinglingResponse {

    /**
     * 成功响应码
     */
    public static final String SUCCESS_CODE = "200";

    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 是否成功
     */
    @SerializedName("success")
    private Boolean success;

    /**
     * 判断请求失败
     *
     * @return 执行结果
     */
    public boolean failure() {
        return !success();
    }

    /**
     * 判断请求成功
     *
     * @return 执行结果
     */
    public boolean success() {
        return Boolean.TRUE.equals(success) && SUCCESS_CODE.equals(code);
    }

}
